There are still errors to fix:
##1. modern-framework-optimizer.ts: "[{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 195,
	"startColumn": 18,
	"endLineNumber": 195,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 227,
	"startColumn": 26,
	"endLineNumber": 227,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 229,
	"startColumn": 34,
	"endLineNumber": 229,
	"endColumn": 37
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 233,
	"startColumn": 26,
	"endLineNumber": 233,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 268,
	"startColumn": 26,
	"endLineNumber": 268,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 268,
	"startColumn": 51,
	"endLineNumber": 268,
	"endColumn": 54
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 283,
	"startColumn": 26,
	"endLineNumber": 283,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 312,
	"startColumn": 26,
	"endLineNumber": 312,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 312,
	"startColumn": 50,
	"endLineNumber": 312,
	"endColumn": 53
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 322,
	"startColumn": 26,
	"endLineNumber": 322,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 350,
	"startColumn": 26,
	"endLineNumber": 350,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 414,
	"startColumn": 26,
	"endLineNumber": 414,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 445,
	"startColumn": 24,
	"endLineNumber": 445,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 452,
	"startColumn": 24,
	"endLineNumber": 452,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 464,
	"startColumn": 26,
	"endLineNumber": 464,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 540,
	"startColumn": 26,
	"endLineNumber": 540,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 552,
	"startColumn": 26,
	"endLineNumber": 552,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 553,
	"startColumn": 26,
	"endLineNumber": 553,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 564,
	"startColumn": 26,
	"endLineNumber": 564,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 565,
	"startColumn": 26,
	"endLineNumber": 565,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 578,
	"startColumn": 25,
	"endLineNumber": 578,
	"endColumn": 28
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 587,
	"startColumn": 31,
	"endLineNumber": 587,
	"endColumn": 34
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 592,
	"startColumn": 24,
	"endLineNumber": 592,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 593,
	"startColumn": 24,
	"endLineNumber": 593,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 647,
	"startColumn": 22,
	"endLineNumber": 647,
	"endColumn": 25
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 651,
	"startColumn": 22,
	"endLineNumber": 651,
	"endColumn": 25
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 655,
	"startColumn": 22,
	"endLineNumber": 655,
	"endColumn": 25
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 659,
	"startColumn": 22,
	"endLineNumber": 659,
	"endColumn": 25
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 694,
	"startColumn": 24,
	"endLineNumber": 694,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 695,
	"startColumn": 24,
	"endLineNumber": 695,
	"endColumn": 27
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/modern-framework-optimizer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 696,
	"startColumn": 24,
	"endLineNumber": 696,
	"endColumn": 27
}]"


##2. component-library-detector.ts: "[{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 157,
	"startColumn": 18,
	"endLineNumber": 157,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 211,
	"startColumn": 26,
	"endLineNumber": 211,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 211,
	"startColumn": 49,
	"endLineNumber": 211,
	"endColumn": 52
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 526,
	"startColumn": 47,
	"endLineNumber": 526,
	"endColumn": 50
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 842,
	"startColumn": 25,
	"endLineNumber": 842,
	"endColumn": 28
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 855,
	"startColumn": 18,
	"endLineNumber": 855,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 890,
	"startColumn": 18,
	"endLineNumber": 890,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 902,
	"startColumn": 18,
	"endLineNumber": 902,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 913,
	"startColumn": 18,
	"endLineNumber": 913,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 929,
	"startColumn": 18,
	"endLineNumber": 929,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 940,
	"startColumn": 18,
	"endLineNumber": 940,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 1017,
	"startColumn": 27,
	"endLineNumber": 1017,
	"endColumn": 30
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'library' is defined but never used. Allowed unused args must match /^_/u.",
	"source": "eslint",
	"startLineNumber": 712,
	"startColumn": 35,
	"endLineNumber": 712,
	"endColumn": 42
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'library' is defined but never used. Allowed unused args must match /^_/u.",
	"source": "eslint",
	"startLineNumber": 736,
	"startColumn": 30,
	"endLineNumber": 736,
	"endColumn": 37
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'issues' is assigned a value but never used.",
	"source": "eslint",
	"startLineNumber": 762,
	"startColumn": 17,
	"endLineNumber": 762,
	"endColumn": 23
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'recommendations' is assigned a value but never used.",
	"source": "eslint",
	"startLineNumber": 790,
	"startColumn": 17,
	"endLineNumber": 790,
	"endColumn": 32
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/component-library-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'library' is defined but never used. Allowed unused args must match /^_/u.",
	"source": "eslint",
	"startLineNumber": 858,
	"startColumn": 9,
	"endLineNumber": 858,
	"endColumn": 16
}]" 


##3. content-quality-analyzer.ts: "[{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/content-quality-analyzer.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-unused-vars",
		"target": {
			"$mid": 1,
			"path": "/rules/no-unused-vars",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 4,
	"message": "'_readabilityLibrary' is assigned a value but never used.",
	"source": "eslint",
	"startLineNumber": 15,
	"startColumn": 3,
	"endLineNumber": 15,
	"endColumn": 22
}]"

##4. cms-detector.ts: "[{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 103,
	"startColumn": 25,
	"endLineNumber": 103,
	"endColumn": 28
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 311,
	"startColumn": 18,
	"endLineNumber": 311,
	"endColumn": 21
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "no-useless-escape",
		"target": {
			"$mid": 1,
			"path": "/docs/latest/rules/no-useless-escape",
			"scheme": "https",
			"authority": "eslint.org"
		}
	},
	"severity": 8,
	"message": "Unnecessary escape character: \\/.",
	"source": "eslint",
	"startLineNumber": 384,
	"startColumn": 84,
	"endLineNumber": 384,
	"endColumn": 85
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 512,
	"startColumn": 26,
	"endLineNumber": 512,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 512,
	"startColumn": 53,
	"endLineNumber": 512,
	"endColumn": 56
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 552,
	"startColumn": 26,
	"endLineNumber": 552,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 552,
	"startColumn": 58,
	"endLineNumber": 552,
	"endColumn": 61
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 587,
	"startColumn": 26,
	"endLineNumber": 587,
	"endColumn": 29
},{
	"resource": "/d:/Web projects/Comply Checker/backend/src/compliance/wcag/utils/cms-detector.ts",
	"owner": "eslint",
	"code": {
		"value": "@typescript-eslint/no-explicit-any",
		"target": {
			"$mid": 1,
			"path": "/rules/no-explicit-any",
			"scheme": "https",
			"authority": "typescript-eslint.io"
		}
	},
	"severity": 8,
	"message": "Unexpected any. Specify a different type.",
	"source": "eslint",
	"startLineNumber": 587,
	"startColumn": 52,
	"endLineNumber": 587,
	"endColumn": 55
}]"